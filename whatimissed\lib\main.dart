//案例2,对stateful和stateless的巩固
import 'dart:io';

import 'package:flutter/material.dart';
// import 'package:whatimissed/daliy/HelloFlutter.dart';

main() => runApp(MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(home: CSYHomePage());
  }
}

class CSYHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("商品列表")),
      body: CSYHomeContent(),
    );
  }
}

class CSYHomeContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //商品复用的widget
        CSYHomeProductItem(
          "Apple",
          "Macbook",
          "https://img.ixintu.com/download/jpg/201911/e25b904bc42a74d7d77aed81e66d772c.jpg!con",
        ),
        CSYHomeProductItem(
          "Apple",
          "Macbook",
          "https://img.ixintu.com/download/jpg/201911/e25b904bc42a74d7d77aed81e66d772c.jpg!con",
        ),
        CSYHomeProductItem(
          "Apple",
          "Macbook",
          "https://www.bing.com/images/search?view=detailV2&ccid=UCJ8lRxv&id=2FA529D99D38D247DE30D7B42C218A88F151D5D9&thid=OIP.UCJ8lRxvRFf9PL48RIADYgHaG2&mediaurl=https%3a%2f%2fpic.616pic.com%2fphotoone%2f00%2f07%2f60%2f6197663c633195005.jpg!%2ffw%2f1120&exph=1037&expw=1120&q=%e8%8b%b9%e6%9e%9c%e5%9b%be%e7%89%87&simid=608006326139640286&FORM=IRPRST&ck=8E28F91423BFADFFF732366B8F796C1F&selectedIndex=2&itb=0&qpvt=%e8%8b%b9%e6%9e%9c%e5%9b%be%e7%89%87",
        ),
      ],
    );
  }
}

class CSYHomeProductItem extends StatelessWidget {
  final String title;
  final String desc;
  final String imageURL;

  CSYHomeProductItem(this.title, this.desc, this.imageURL);

  @override
  Widget build(BuildContext context) {
    return Column(children: [Text(title), Text(desc), Image.network(imageURL)]);
  }
}
